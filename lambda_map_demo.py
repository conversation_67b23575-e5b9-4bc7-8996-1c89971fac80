# 试用lambda函数和map()函数，实现列表l = [1,2,3,4,5]到[1,0,1,0,1]的映射，即奇数对应1，偶数对应0

# 原始列表
l = [1, 2, 3, 4, 5]

# 方法1：最简洁的实现（推荐）
result1 = list(map(lambda x: x % 2, l))

# 方法2：更明确的条件表达式
result2 = list(map(lambda x: 1 if x % 2 == 1 else 0, l))

# 方法3：使用布尔值转整数
result3 = list(map(lambda x: int(x % 2 == 1), l))

print("=" * 50)
print("Lambda函数和map()函数实现奇偶映射")
print("=" * 50)
print("原始列表:", l)
print("方法1结果:", result1)
print("方法2结果:", result2)
print("方法3结果:", result3)
"试解释”==“和”is“的不同，举一个对于”==“成立而对于”is“不成立的例子"
# 验证结果
expected = [1, 0, 1, 0, 1]
print("\n期望结果:", expected)
print("方法1正确:", result1 == expected)
print("方法2正确:", result2 == expected)
print("方法3正确:", result3 == expected)

# 详细解释每种方法
print("\n" + "=" * 50)
print("方法解释:")
print("-" * 50)
print("方法1: lambda x: x % 2")
print("  - 利用取模运算，奇数%2=1，偶数%2=0")
print("  - 最简洁的实现方式")

print("\n方法2: lambda x: 1 if x % 2 == 1 else 0")
print("  - 使用条件表达式明确判断奇偶")
print("  - 逻辑更清晰易懂")

print("\n方法3: lambda x: int(x % 2 == 1)")
print("  - 先判断是否为奇数得到布尔值")
print("  - 再转换为整数：True->1, False->0")

# 扩展：处理更大的列表
print("\n" + "=" * 50)
print("扩展测试:")
print("-" * 50)
large_list = list(range(1, 21))  # [1, 2, 3, ..., 20]
large_result = list(map(lambda x: x % 2, large_list))
print("大列表:", large_list)
print("映射结果:", large_result)

# 对比其他实现方式
print("\n" + "=" * 50)
print("其他实现方式对比:")
print("-" * 50)

# 使用列表推导式
list_comp = [x % 2 for x in l]
print("列表推导式:", list_comp)

# 使用普通函数
def odd_even_map(x):
    return x % 2

func_result = list(map(odd_even_map, l))
print("普通函数+map:", func_result)

# 使用for循环
loop_result = []
for x in l:
    loop_result.append(x % 2)
print("for循环:", loop_result)

print("\n所有方法结果一致:", all([
    result1 == expected,
    result2 == expected,
    result3 == expected,
    list_comp == expected,
    func_result == expected,
    loop_result == expected
]))
