# Python中"=="和"is"的区别详解

print("=" * 60)
print("Python中 '==' 和 'is' 的区别")
print("=" * 60)

print("\n1. 基本概念:")
print("-" * 30)
print("== : 比较两个对象的值是否相等（值相等性）")
print("is : 比较两个对象是否是同一个对象（身份相等性）")

print("\n2. 经典例子 - 列表:")
print("-" * 30)
# 创建两个内容相同但不是同一个对象的列表
list1 = [1, 2, 3]
list2 = [1, 2, 3]
list3 = list1  # 指向同一个对象

print(f"list1 = {list1}")
print(f"list2 = {list2}")
print(f"list3 = list1 (指向同一个对象)")

print(f"\nlist1 == list2: {list1 == list2}")  # True - 值相等
print(f"list1 is list2: {list1 is list2}")    # False - 不是同一个对象
print(f"list1 is list3: {list1 is list3}")    # True - 是同一个对象

print(f"\nid(list1): {id(list1)}")  # 内存地址
print(f"id(list2): {id(list2)}")    # 不同的内存地址
print(f"id(list3): {id(list3)}")    # 与list1相同的内存地址

print("\n3. 字符串例子:")
print("-" * 30)
# 字符串的特殊情况
str1 = "hello"
str2 = "hello"
str3 = "hel" + "lo"
str4 = "hello world"[0:5]

print(f"str1 = '{str1}'")
print(f"str2 = '{str2}'")
print(f"str3 = 'hel' + 'lo' = '{str3}'")
print(f"str4 = 'hello world'[0:5] = '{str4}'")

print(f"\nstr1 == str2: {str1 == str2}")  # True
print(f"str1 is str2: {str1 is str2}")    # True (字符串驻留)
print(f"str1 == str3: {str1 == str3}")    # True
print(f"str1 is str3: {str1 is str3}")    # True (编译时优化)
print(f"str1 == str4: {str1 == str4}")    # True
print(f"str1 is str4: {str1 is str4}")    # False (运行时创建)

print("\n4. 数字例子:")
print("-" * 30)
# 小整数缓存
a = 256
b = 256
c = 257
d = 257

print(f"a = 256, b = 256")
print(f"c = 257, d = 257")

print(f"\na == b: {a == b}")  # True
print(f"a is b: {a is b}")    # True (小整数缓存)
print(f"c == d: {c == d}")    # True
print(f"c is d: {c is d}")    # False (超出缓存范围)

print("\n5. 自定义类例子:")
print("-" * 30)

class Person:
    def __init__(self, name, age):
        self.name = name
        self.age = age
    
    def __eq__(self, other):
        if isinstance(other, Person):
            return self.name == other.name and self.age == other.age
        return False
    
    def __repr__(self):
        return f"Person('{self.name}', {self.age})"

person1 = Person("张三", 25)
person2 = Person("张三", 25)
person3 = person1

print(f"person1 = {person1}")
print(f"person2 = {person2}")
print(f"person3 = person1")

print(f"\nperson1 == person2: {person1 == person2}")  # True (重写了__eq__)
print(f"person1 is person2: {person1 is person2}")    # False (不同对象)
print(f"person1 is person3: {person1 is person3}")    # True (同一对象)

print("\n6. None的特殊情况:")
print("-" * 30)
x = None
y = None

print(f"x = None, y = None")
print(f"x == y: {x == y}")    # True
print(f"x is y: {x is y}")    # True (None是单例)
print("推荐用法: x is None 而不是 x == None")

print("\n7. 实际应用建议:")
print("-" * 30)
print("使用 == 的场景:")
print("  - 比较值是否相等")
print("  - 比较数字、字符串、列表内容等")

print("\n使用 is 的场景:")
print("  - 与 None 比较: if x is None")
print("  - 与 True/False 比较: if flag is True")
print("  - 检查是否是同一个对象")
print("  - 性能要求高的场景（is比==快）")

print("\n8. 性能对比:")
print("-" * 30)
import time

# 性能测试
test_list = [1, 2, 3] * 1000

start = time.time()
for _ in range(100000):
    result = test_list is test_list
end = time.time()
is_time = end - start

start = time.time()
for _ in range(100000):
    result = test_list == test_list
end = time.time()
eq_time = end - start

print(f"'is' 操作耗时: {is_time:.6f} 秒")
print(f"'==' 操作耗时: {eq_time:.6f} 秒")
print(f"'is' 比 '==' 快 {eq_time/is_time:.1f} 倍")
