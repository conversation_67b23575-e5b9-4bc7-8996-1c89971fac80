# 欧几里得算法（减法版本）求最大公约数 - 递归实现

def gcd(a, b):
    """
    用欧几里得算法（减法版本）求两个整数的最大公约数
    算法思想：用大数减去小数，保留差和较小的数，直至两数相等
    
    参数:
        a, b: 两个正整数
    
    返回:
        最大公约数
    """
    return a if a == b else gcd(max(a, b) - min(a, b), min(a, b))

# 更简洁的版本（一行代码）
def gcd_oneline(a, b):
    """最简洁版本 - 一行代码实现"""
    return a if a == b else gcd_oneline(max(a, b) - min(a, b), min(a, b))

# 带过程显示的版本
def gcd_with_steps(a, b, step=1):
    """
    带步骤显示的版本，便于理解算法过程
    """
    print(f"步骤{step}: gcd({a}, {b})")
    
    if a == b:
        print(f"结果: {a}")
        return a
    else:
        larger = max(a, b)
        smaller = min(a, b)
        diff = larger - smaller
        print(f"       {larger} - {smaller} = {diff}")
        return gcd_with_steps(diff, smaller, step + 1)

# 测试函数
def test_gcd(): 
    # 测试用例
    test_cases = [
        (48, 18),
        (56, 42),
        (100, 25),
        (17, 13),
        (24, 36)
    ]
    for a, b in test_cases:
        result = gcd(a, b)
        print(f"gcd({a}, {b}) = {result}")

if __name__ == "__main__":
    test_gcd()
