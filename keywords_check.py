# Python关键字检查程序

import keyword

print("=" * 60)
print("Python关键字检查")
print("=" * 60)

# 待检查的词汇列表
words_to_check = ["this", "self", "TRUE", "None", "assert", "eval", "lambda", "del"]

print("检查以下词汇是否为Python关键字:")
print(f"词汇列表: {words_to_check}")
print("\n" + "-" * 60)

# 逐个检查
keywords_found = []
not_keywords = []

for word in words_to_check:
    is_keyword = keyword.iskeyword(word)
    status = "✓ 是关键字" if is_keyword else "✗ 不是关键字"
    print(f"{word:8} -> {status}")
    
    if is_keyword:
        keywords_found.append(word)
    else:
        not_keywords.append(word)

print("\n" + "=" * 60)
print("总结:")
print("=" * 60)

print(f"是关键字的: {keywords_found}")
print(f"不是关键字的: {not_keywords}")

print("\n" + "-" * 60)
print("详细分析:")
print("-" * 60)

# 详细分析每个词汇
analysis = {
    "this": "不是关键字。Python中没有'this'，使用'self'表示实例对象",
    "self": "不是关键字。是约定俗成的参数名，表示类的实例对象",
    "TRUE": "不是关键字。Python中布尔值是'True'（首字母大写）",
    "None": "是关键字。表示空值/无值的特殊常量",
    "assert": "是关键字。用于调试，断言某个条件为真",
    "eval": "不是关键字。是内置函数，用于执行字符串表达式",
    "lamba": "不是关键字。拼写错误，正确的关键字是'lambda'",
    "del": "是关键字。用于删除对象引用"
}

for word in words_to_check:
    print(f"\n{word}:")
    print(f"  {analysis[word]}")

print("\n" + "=" * 60)
print("Python所有关键字列表:")
print("=" * 60)

all_keywords = keyword.kwlist
print(f"Python {len(all_keywords)} 个关键字:")
for i, kw in enumerate(all_keywords, 1):
    print(f"{i:2d}. {kw}")

print("\n" + "-" * 60)
print("常见易混淆的词汇:")
print("-" * 60)
print("关键字 vs 非关键字:")
print("  True (关键字) vs TRUE (不是)")
print("  False (关键字) vs FALSE (不是)")
print("  None (关键字) vs null (不是)")
print("  lambda (关键字) vs lamba (拼写错误)")
print("  def (关键字) vs function (不是)")
print("  class (关键字) vs Class (不是)")

print("\n特殊说明:")
print("  - self: 虽然不是关键字，但是强烈约定的参数名")
print("  - __init__: 不是关键字，是特殊方法名")
print("  - eval, exec: 不是关键字，是内置函数")
print("  - this: Python中不存在，其他语言(如JavaScript)中有")
