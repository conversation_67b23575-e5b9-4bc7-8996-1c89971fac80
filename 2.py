a = []     
b= []          


for i in range(100):
    c = (i % 7 == 0) 
    d = '7' in str(i)
    
    if c:
        a.append(i)
    
    if d:
        b.append(i)

print("1. 7的倍数：")
print(f"   {a}")
print(f"   共 {len(a)} 个")

print("\n2. 包含数字'7'的数字：")
print(f"   {b}")
print(f"   共 {len(b)} 个")
# 帮我完成第三道题目：“在某项体育比赛里，有五名裁判给一个运动员主观评分。除去一个最高分、一个最低分，运动员的最终得分是其它三名裁判的平均分。五名裁判的评分作为一组数据，试编写一个函数处理这组数据，计算运动员的最终得分。”
