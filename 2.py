# 用遍历法编写一个程序，找出所有100以内7的倍数和包含数字"7"的数字

print("100以内7的倍数和包含数字'7'的数字")
print("=" * 50)

# 存储结果的列表
multiples_of_7 = []      # 7的倍数
contains_7 = []          # 包含数字7的数字
both_conditions = []     # 既是7的倍数又包含数字7的数字

# 遍历0到99的所有数字
for i in range(100):
    is_multiple_of_7 = (i % 7 == 0)  # 判断是否是7的倍数
    contains_digit_7 = '7' in str(i)  # 判断是否包含数字7
    
    if is_multiple_of_7:
        multiples_of_7.append(i)
    
    if contains_digit_7:
        contains_7.append(i)
    
    # 如果既是7的倍数又包含数字7
    if is_multiple_of_7 and contains_digit_7:
        both_conditions.append(i)

# 输出结果
print("1. 7的倍数：")
print(f"   {multiples_of_7}")
print(f"   共 {len(multiples_of_7)} 个")

print("\n2. 包含数字'7'的数字：")
print(f"   {contains_7}")
print(f"   共 {len(contains_7)} 个")

print("\n3. 既是7的倍数又包含数字'7'的数字：")
print(f"   {both_conditions}")
print(f"   共 {len(both_conditions)} 个")

# 详细显示过程（可选）
print("\n" + "=" * 50)
print("详细遍历过程：")
print("-" * 50)

for i in range(100):
    is_multiple_of_7 = (i % 7 == 0)
    contains_digit_7 = '7' in str(i)
    
    status = []
    if is_multiple_of_7:
        status.append("7的倍数")
    if contains_digit_7:
        status.append("包含数字7")
    
    if status:
        print(f"{i:2d}: {', '.join(status)}")
