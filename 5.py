# 密码强度检验程序

def check_password(password):
    """
    检验密码是否符合要求
    要求：包含大写字母、小写字母、数字和特殊符号四种，长度6-12字符
    
    参数:
        password: 待检验的密码字符串
    
    返回:
        True: 符合要求, False: 不符合要求
    """
    return (6 <= len(password) <= 12 and
            any(c.isupper() for c in password) and
            any(c.islower() for c in password) and
            any(c.isdigit() for c in password) and
            any(not c.isalnum() for c in password))

# 更简洁的一行版本
def check_password_oneline(pwd):
    """最简洁版本 - 一行代码实现"""
    return 6 <= len(pwd) <= 12 and all(any(f(c) for c in pwd) for f in [str.isupper, str.islower, str.isdigit, lambda x: not x.isalnum()])

# 带详细信息的版本
def check_password_detailed(password):
    """
    带详细检查信息的版本
    """
    length_ok = 6 <= len(password) <= 12
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(not c.isalnum() for c in password)
    
    print(f"密码: '{password}'")
    print(f"长度(6-12): {'✓' if length_ok else '✗'} (当前长度: {len(password)})")
    print(f"大写字母: {'✓' if has_upper else '✗'}")
    print(f"小写字母: {'✓' if has_lower else '✗'}")
    print(f"数字: {'✓' if has_digit else '✗'}")
    print(f"特殊符号: {'✓' if has_special else '✗'}")
    
    result = length_ok and has_upper and has_lower and has_digit and has_special
    print(f"结果: {'通过' if result else '不通过'}")
    print("-" * 40)
    
    return result

# 测试函数
def test_passwords():
    print("密码强度检验程序")
    print("=" * 50)
    print("要求：包含大写字母、小写字母、数字和特殊符号，长度6-12字符")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        "Abc123!",      # 符合要求
        "abc123!",      # 缺少大写字母
        "ABC123!",      # 缺少小写字母
        "Abcdef!",      # 缺少数字
        "Abc123",       # 缺少特殊符号
        "Ab1!",         # 长度不够
        "Abc123!@#$%^&*()", # 长度超出
        "MyPass1@",     # 符合要求
        "Test2023#",    # 符合要求
        "hello",        # 多项不符合
    ]
    
    print("快速检验结果:")
    for pwd in test_cases:
        result = check_password(pwd)
        print(f"'{pwd}' -> {'通过' if result else '不通过'}")
    
    print("\n" + "=" * 50)
    print("详细检验过程:")
    print("-" * 50)
    
    # 选择几个例子显示详细过程
    detailed_tests = ["Abc123!", "abc123!", "Ab1!", "MyPass1@"]
    for pwd in detailed_tests:
        check_password_detailed(pwd)

if __name__ == "__main__":
    test_passwords()
