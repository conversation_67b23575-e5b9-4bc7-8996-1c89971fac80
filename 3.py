def simple_calculate_final_score(scores):

    scores_copy = scores.copy()
    scores_copy.remove(max(scores_copy))  # 移除最高分
    scores_copy.remove(min(scores_copy))  # 移除最低分
    return sum(scores_copy) / len(scores_copy)  # 返回平均分

def display_score_details(scores):

    print(f"原始评分: {scores}")
    print(f"最高分: {max(scores)}")
    print(f"最低分: {min(scores)}")
    
    # 计算最终得分
    final_score = simple_calculate_final_score(scores)
    
    # 显示去掉最高分和最低分后的评分
    scores_copy = scores.copy()
    scores_copy.remove(max(scores))
    scores_copy.remove(min(scores))
    
    print(f"去掉最高分和最低分后的评分: {scores_copy}")
    print(f"最终得分: {final_score:.2f}")
    print("-" * 40)

# 测试函数
def main():
    # 交互式输入
    print("请输入五名裁判的评分:")

    user_scores = []
    for i in range(5):
        score = float(input(f"请输入第{i+1}名裁判的评分: "))
        user_scores.append(score)
        
    print("\n用户输入的评分:")
    display_score_details(user_scores)


if __name__ == "__main__":
    main()
