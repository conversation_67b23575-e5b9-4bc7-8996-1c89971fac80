def calculate_final_score(scores):

    scores_copy = scores.copy()
    scores_copy.remove(max(scores_copy))  # 移除最高分
    scores_copy.remove(min(scores_copy))  # 移除最低分
    return sum(scores_copy) / len(scores_copy)  # 返回平均分

# 测试函数
def main():
    # 交互式输入
    print("请输入五名裁判的评分:")

    scores = []
    for i in range(5):
        score = float(input(f"请输入第{i+1}名裁判的评分: "))
        scores.append(score)
    
    final_score = calculate_final_score(scores)
    print(f"最终得分: {final_score:.2f}")


if __name__ == "__main__":
    main()

# 帮我完成第四道题：“试用欧几里得算法设计求取两个整数的最大公约数的函数。算法的思想是：用大数减去小数，保留差和较小的数，再继续计算这两个数的最大公约数，直至两数相等。请根据这一算法思想，用递归法编写该函数。”要求代码越简洁越好。
# 帮我完成第五道题：“某电脑登陆系统对用户设置密码的要求是：必须包含大写字母、小写字母、数字和除此以外的其他特殊符号四种，长度在6到12个字符之间，试编写一段程序，检验输入的字符串是否符合该要求。”要求代码越简洁越好。