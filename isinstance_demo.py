# isinstance()函数详解 - Python中没有instance()函数

print("=" * 60)
print("Python中的isinstance()函数详解")
print("=" * 60)

print("注意：Python中没有instance()函数，只有isinstance()函数")
print()

# isinstance()函数的基本用法
print("1. isinstance()函数的返回类型:")
print("-" * 40)

# isinstance()返回布尔值
result1 = isinstance(5, int)
result2 = isinstance("hello", str)
result3 = isinstance([1, 2, 3], list)

print(f"isinstance(5, int) = {result1}")
print(f"type(isinstance(5, int)) = {type(result1)}")
print(f"isinstance('hello', str) = {result2}")
print(f"type(isinstance('hello', str)) = {type(result2)}")
print(f"isinstance([1, 2, 3], list) = {result3}")
print(f"type(isinstance([1, 2, 3], list)) = {type(result3)}")

print(f"\n结论: isinstance()函数返回 {type(result1).__name__} 类型的对象")

print("\n" + "=" * 60)
print("2. isinstance()函数的详细用法:")
print("-" * 40)

# 各种数据类型的检查
test_values = [
    (42, int),
    (3.14, float),
    ("Python", str),
    ([1, 2, 3], list),
    ((1, 2, 3), tuple),
    ({"a": 1}, dict),
    ({1, 2, 3}, set),
    (True, bool),
    (None, type(None))
]

for value, data_type in test_values:
    result = isinstance(value, data_type)
    print(f"isinstance({value!r:12}, {data_type.__name__:8}) = {result}")

print("\n" + "-" * 40)
print("3. 多类型检查:")
print("-" * 40)

# isinstance可以检查多个类型
value = 42
result_multi = isinstance(value, (int, float, str))
print(f"isinstance(42, (int, float, str)) = {result_multi}")
print(f"返回类型: {type(result_multi)}")

value = "hello"
result_multi2 = isinstance(value, (int, float))
print(f"isinstance('hello', (int, float)) = {result_multi2}")
print(f"返回类型: {type(result_multi2)}")

print("\n" + "=" * 60)
print("4. 自定义类的isinstance检查:")
print("-" * 40)

class Animal:
    def __init__(self, name):
        self.name = name

class Dog(Animal):
    def bark(self):
        return "Woof!"

class Cat(Animal):
    def meow(self):
        return "Meow!"

# 创建实例
dog = Dog("旺财")
cat = Cat("咪咪")

# isinstance检查
print(f"dog = Dog('旺财')")
print(f"isinstance(dog, Dog) = {isinstance(dog, Dog)}")
print(f"isinstance(dog, Animal) = {isinstance(dog, Animal)}")  # 继承关系
print(f"isinstance(dog, Cat) = {isinstance(dog, Cat)}")

print(f"\ncat = Cat('咪咪')")
print(f"isinstance(cat, Cat) = {isinstance(cat, Cat)}")
print(f"isinstance(cat, Animal) = {isinstance(cat, Animal)}")  # 继承关系
print(f"isinstance(cat, Dog) = {isinstance(cat, Dog)}")

print(f"\n所有isinstance()调用的返回类型都是: {type(isinstance(dog, Dog))}")

print("\n" + "=" * 60)
print("5. isinstance() vs type()的区别:")
print("-" * 40)

print("isinstance()考虑继承关系，type()不考虑:")
print(f"type(dog) == Dog: {type(dog) == Dog}")
print(f"type(dog) == Animal: {type(dog) == Animal}")
print(f"isinstance(dog, Dog): {isinstance(dog, Dog)}")
print(f"isinstance(dog, Animal): {isinstance(dog, Animal)}")

print("\n" + "=" * 60)
print("6. 实际应用示例:")
print("-" * 40)

def process_data(data):
    """根据数据类型进行不同处理"""
    if isinstance(data, str):
        return f"字符串长度: {len(data)}"
    elif isinstance(data, (int, float)):
        return f"数字的平方: {data ** 2}"
    elif isinstance(data, list):
        return f"列表元素个数: {len(data)}"
    elif isinstance(data, dict):
        return f"字典键的个数: {len(data.keys())}"
    else:
        return f"未知类型: {type(data)}"

# 测试不同类型的数据
test_data = ["Python", 5, 3.14, [1, 2, 3], {"a": 1, "b": 2}, dog]

for data in test_data:
    result = process_data(data)
    print(f"process_data({data!r}) -> {result}")

print("\n" + "=" * 60)
print("总结:")
print("=" * 60)
print("• isinstance()函数返回 bool 类型的对象 (True 或 False)")
print("• 用于检查对象是否是指定类型的实例")
print("• 支持多类型检查: isinstance(obj, (type1, type2, ...))")
print("• 考虑继承关系，比type()更灵活")
print("• 是类型检查的推荐方式")

# 验证没有instance()函数
print("\n" + "-" * 40)
print("验证Python中没有instance()函数:")
try:
    # 尝试调用instance()函数
    result = instance(5, int)  # 这会报错
except NameError as e:
    print(f"错误: {e}")
    print("确认：Python中没有instance()函数，只有isinstance()函数")
